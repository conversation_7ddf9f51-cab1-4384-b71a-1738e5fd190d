﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using System.Linq;

namespace Pondres.Omnia.Control.Integrations.Customer.Components
{
    public partial class CustomerSelection : ComponentBase
    {
        [Inject]
        public ILocalStorageService LocalStorageService { get; private set; }

        [Parameter]
        public IEnumerable<CustomerModel> Customers { get; set; }

        [Parameter]
        public string SelectedCustomerId { get; set; }

        [Parameter]
        public EventCallback<string> CustomerChanged { get; set; }

        public bool ShowTestCustomers { get; set; } = false;

        private IEnumerable<CustomerModel> _filteredCustomers;
        
        private IEnumerable<CustomerModel> FilteredCustomers 
        { 
            get 
            {
                if (_filteredCustomers == null)
                {
                    UpdateFilteredCustomers();
                }
                return _filteredCustomers;
            }
        }

        protected override void OnParametersSet()
        {
            UpdateFilteredCustomers();
            base.OnParametersSet();
        }
        
        private void UpdateFilteredCustomers()
        {
            _filteredCustomers = ShowTestCustomers 
                ? Customers?.Where(c => c.Id.Contains("_TEST") || c.Id == "K00021")
                : Customers?.Where(c => !c.Id.Contains("_TEST") && c.Id != "K00021");
        }

        private async Task OnValueChangedAsync(string customerId)
        {
            if (CustomerChanged.HasDelegate)
                await CustomerChanged.InvokeAsync(customerId);
        }
        
        private async Task OnShowTestCustomersChangedAsync(bool value)
        {
            ShowTestCustomers = value;
            UpdateFilteredCustomers();
            
            // If current selection is not in filtered list, select first available
            if (FilteredCustomers?.Any() == true && 
                !FilteredCustomers.Any(c => c.Id == SelectedCustomerId))
            {
                var firstCustomer = FilteredCustomers.FirstOrDefault();
                if (firstCustomer != null)
                {
                    await OnValueChangedAsync(firstCustomer.Id);
                }
            }
            
            StateHasChanged();
        }
    }
}
