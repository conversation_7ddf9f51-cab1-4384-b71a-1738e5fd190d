﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Telerik.Blazor.Common.Export.Excel;
using Telerik.Blazor.Components;
using Telerik.Blazor.Components.Grid;

namespace Pondres.Omnia.Control.Integrations.Customer.Components
{
    /// <summary>
    /// Class SelectableCustomerGridPageBase.
    /// Implements the <see cref="Pondres.Omnia.Control.Integrations.Customer.Components.CustomerPageBase" />
    ///
    /// SelectableGridBase is the same page and holds all the required functionality. When we implement MVVM in Control this page
    /// can be removed since the UI logic will no longer depend on the CustomerPageBase type. This should be handled by the ViewModel.
    /// See https://dev.azure.com/pondresnl/Omnia/_git/Pondres.Omnia.Control?path=/Integrations/Pondres.Omnia.Control.Integrations.Common/Components/Base/ViewBase.cs&version=GBfeature/4084-MVVM&_a=contents
    /// for the base type which it then uses
    ///
    /// MVVM ticket in Jira: https://poncon.atlassian.net/browse/OMNIA-4084
    /// </summary>
    /// <typeparam name="TValue">The type of the t value.</typeparam>
    /// <seealso cref="Pondres.Omnia.Control.Integrations.Customer.Components.CustomerPageBase" />
    [Obsolete("This page should be removed when switching to MVVM")]
    public class SelectableCustomerGridPageBase<TValue> : CustomerPageBase
        where TValue : class, new()
    {
        [Inject]
        protected IClipboardManager ClipboardManager { get; set; }

        [Inject]
        protected IToastService ToastService { get; set; }

        public TelerikGrid<TValue> DataGrid { get; protected set; }
        protected List<string> SelectedColumns { get; } = new List<string>();
        protected bool HasSelectedColumns => SelectedColumns?.Any() ?? false;
        protected bool HasSelectedItems => DataGrid?.SelectedItems?.Any() ?? false;
        protected bool HasData => DataGrid?.Data?.Any() ?? false;

        protected void OnColumnSelect(SelectableGridColumn column)
        {
            if (column.Field == null)
                throw new InvalidOperationException("Field cannot be null.");

            if (column.IsSelected)
            {
                if (!SelectedColumns.Contains(column.Field))
                    SelectedColumns.Add(column.Field);
            }
            else
                SelectedColumns.Remove(column.Field);
        }

        protected void OnBeforeCsvExport(GridBeforeCsvExportEventArgs args)
        {
            List<GridCsvExportColumn> columns = null;

            var actualColumns = args.Columns.Where(item => !item.Field.IsNullOrWhiteSpace()).ToList();

            if (HasSelectedColumns)
                columns = actualColumns.Where(item => SelectedColumns.Exists(column => item.Field.EndsWith(column))).ToList();
            else
                columns = actualColumns.ToList();

            if (columns.Any())
            {
                args.Columns = columns;

                if (HasSelectedItems)
                    args.Data = DataGrid.SelectedItems;
                else
                    args.Data = DataGrid.Data;

                args.IsCancelled = false;
            }
        }

        protected async Task CopySelectedAsync()
        {
            try
            {
                IEnumerable<string> contents = null;

                var columns = SelectedColumns;

                if (!columns.Any())
                {
                    var state = DataGrid.GetState();
                    columns = state.ColumnStates.Where(item => item.Visible != false &&
                                                               !item.Field.IsNullOrWhiteSpace() &&
                                                               (item.Id.IsNullOrWhiteSpace() || !item.Id.Contains("Disabled")))
                                                .Select(item => item.Field)
                                                .ToList();
                }

                if (HasSelectedItems)
                    contents = DataGrid.SelectedItems.Select(item => item.ToCSVString(columns));
                else
                    contents = DataGrid.Data.Select(item => item.ToCSVString(columns));

                if (contents != null)
                {
                    await ClipboardManager.CopyToClipboardAsync(string.Join(Environment.NewLine, contents));
                    ToastService.ShowInfo("Copied to clipboard");
                }
            }
            catch (Exception exception)
            {
                ToastService.ShowError(exception.Message);
            }
        }
    }
}